import React, { useContext, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    SafeAreaView,
    ScrollView,
    ActivityIndicator,
    TouchableOpacity,
    Alert,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchProjectDetails } from '../../api/projects/projectApi';
import {
    acceptServiceRequest,
    rejectServiceRequest,
} from '../../api/serviceRequests/serviceRequestApi';
import BackButton from '../Components/Shared/BackButton';
import { showToast } from '../../utils/showToast';

export default function ServiceRequestDetails() {
    const { theme } = useContext(ThemeContext);
    const { serviceRequestId, projectId } = useLocalSearchParams();
    const queryClient = useQueryClient();
    const [isProcessing, setIsProcessing] = useState(false);

    const {
        data: projectData,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['project', projectId],
        queryFn: () => fetchProjectDetails(projectId),
        enabled: !!projectId,
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to fetch project details'
            );
        },
    });

    const acceptMutation = useMutation({
        mutationFn: () => acceptServiceRequest(projectId),
        onSuccess: () => {
            showToast(
                'success',
                'Success',
                'Service request accepted successfully!'
            );
            queryClient.invalidateQueries(['serviceRequests']);
            router.back();
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to accept service request'
            );
        },
        onSettled: () => {
            setIsProcessing(false);
        },
    });

    const rejectMutation = useMutation({
        mutationFn: () => rejectServiceRequest(projectId),
        onSuccess: () => {
            showToast('success', 'Success', 'Service request rejected');
            queryClient.invalidateQueries(['serviceRequests']);
            router.back();
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to reject service request'
            );
        },
        onSettled: () => {
            setIsProcessing(false);
        },
    });

    const project = projectData?.project;
    const requirement = projectData?.requirement;

    const formatDate = (dateString) => {
        if (!dateString) return 'Not specified';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget)) {
            return 'Budget not specified';
        }

        const formatAmount = (amount) => {
            if (amount >= 10000000) {
                return `₹${(amount / 10000000).toFixed(1)} Crore`;
            } else if (amount >= 100000) {
                return `₹${(amount / 100000).toFixed(1)} Lakh`;
            } else if (amount >= 1000) {
                return `₹${(amount / 1000).toFixed(1)} Thousand`;
            }
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        } else if (budget.minBudget) {
            return `From ${formatAmount(budget.minBudget)}`;
        } else if (budget.maxBudget) {
            return `Up to ${formatAmount(budget.maxBudget)}`;
        }
    };

    const formatLocation = (location) => {
        if (!location) return 'Location not specified';
        const parts = [];
        if (location.address) parts.push(location.address);
        if (location.city) parts.push(location.city);
        if (location.state) parts.push(location.state);
        if (location.pincode) parts.push(location.pincode);
        return parts.join(', ') || 'Location not specified';
    };

    const handleAccept = () => {
        Alert.alert(
            'Accept Service Request',
            'Are you sure you want to accept this service request? This will assign you to the project.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Accept',
                    style: 'default',
                    onPress: () => {
                        setIsProcessing(true);
                        acceptMutation.mutate();
                    },
                },
            ]
        );
    };

    const handleReject = () => {
        Alert.alert(
            'Reject Service Request',
            'Are you sure you want to reject this service request? This action cannot be undone.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Reject',
                    style: 'destructive',
                    onPress: () => {
                        setIsProcessing(true);
                        rejectMutation.mutate();
                    },
                },
            ]
        );
    };

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.header}>
                    <BackButton />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Service Request
                    </Text>
                    <View style={styles.headerRight} />
                </View>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Loading project details...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (isError || !project || !requirement) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.header}>
                    <BackButton />
                    <Text
                        style={[
                            styles.headerTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Service Request
                    </Text>
                    <View style={styles.headerRight} />
                </View>
                <View style={styles.errorContainer}>
                    <Ionicons
                        name="alert-circle-outline"
                        size={80}
                        color={theme.ERROR}
                    />
                    <Text
                        style={[
                            styles.errorTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Failed to Load
                    </Text>
                    <Text
                        style={[
                            styles.errorSubtitle,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {error?.message || 'Could not load project details.'}
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <View style={styles.header}>
                <BackButton />
                <Text
                    style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Service Request
                </Text>
                <View style={styles.headerRight} />
            </View>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {/* Project Overview */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Project Overview
                    </Text>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Project Name:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {project.projectName}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Type:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {requirement.projectType}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Construction:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {requirement.constructionType}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Status:
                        </Text>
                        <Text style={[styles.value, { color: theme.PRIMARY }]}>
                            {project.status}
                        </Text>
                    </View>
                </View>

                {/* Location & Timeline */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Location & Timeline
                    </Text>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Location:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {formatLocation(requirement.location)}
                        </Text>
                    </View>

                    {requirement.location?.plotSizeSqFt && (
                        <View style={styles.infoRow}>
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Plot Size:
                            </Text>
                            <Text
                                style={[
                                    styles.value,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {requirement.location.plotSizeSqFt} sq ft
                            </Text>
                        </View>
                    )}

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Expected Start:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {formatDate(requirement.expectedStartDate)}
                        </Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Expected Completion:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {formatDate(requirement.expectedCompletionDate)}
                        </Text>
                    </View>
                </View>

                {/* Budget */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Budget
                    </Text>

                    <View style={styles.infoRow}>
                        <Text
                            style={[
                                styles.label,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Budget Range:
                        </Text>
                        <Text
                            style={[
                                styles.value,
                                { color: theme.PRIMARY, fontWeight: '600' },
                            ]}
                        >
                            {formatBudget(requirement.budget)}
                        </Text>
                    </View>
                </View>

                {/* Design Preferences */}
                {requirement.designPreferences && (
                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Design Preferences
                        </Text>

                        {requirement.designPreferences.floors && (
                            <View style={styles.infoRow}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Floors:
                                </Text>
                                <Text
                                    style={[
                                        styles.value,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {requirement.designPreferences.floors}
                                </Text>
                            </View>
                        )}

                        {requirement.designPreferences.bedrooms && (
                            <View style={styles.infoRow}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Bedrooms:
                                </Text>
                                <Text
                                    style={[
                                        styles.value,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {requirement.designPreferences.bedrooms}
                                </Text>
                            </View>
                        )}

                        {requirement.designPreferences.bathrooms && (
                            <View style={styles.infoRow}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Bathrooms:
                                </Text>
                                <Text
                                    style={[
                                        styles.value,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {requirement.designPreferences.bathrooms}
                                </Text>
                            </View>
                        )}

                        <View style={styles.preferencesGrid}>
                            {requirement.designPreferences.parkingRequired && (
                                <View
                                    style={[
                                        styles.preferenceChip,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="car-outline"
                                        size={16}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.preferenceText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Parking
                                    </Text>
                                </View>
                            )}
                            {requirement.designPreferences.gardenRequired && (
                                <View
                                    style={[
                                        styles.preferenceChip,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="leaf-outline"
                                        size={16}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.preferenceText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Garden
                                    </Text>
                                </View>
                            )}
                            {requirement.designPreferences.vastuCompliance && (
                                <View
                                    style={[
                                        styles.preferenceChip,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="compass-outline"
                                        size={16}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.preferenceText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Vastu
                                    </Text>
                                </View>
                            )}
                        </View>
                    </View>
                )}

                {/* Additional Information */}
                {(requirement.specialInstructions ||
                    requirement.additionalFacilities?.length > 0) && (
                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Additional Information
                        </Text>

                        {requirement.specialInstructions && (
                            <View style={styles.infoRow}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Special Instructions:
                                </Text>
                                <Text
                                    style={[
                                        styles.value,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {requirement.specialInstructions}
                                </Text>
                            </View>
                        )}

                        {requirement.additionalFacilities?.length > 0 && (
                            <View style={styles.infoRow}>
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Additional Facilities:
                                </Text>
                                <View style={styles.facilitiesContainer}>
                                    {requirement.additionalFacilities.map(
                                        (facility, index) => (
                                            <View
                                                key={index}
                                                style={[
                                                    styles.facilityChip,
                                                    {
                                                        backgroundColor:
                                                            theme.PRIMARY +
                                                            '10',
                                                    },
                                                ]}
                                            >
                                                <Text
                                                    style={[
                                                        styles.facilityText,
                                                        {
                                                            color: theme.PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    {facility}
                                                </Text>
                                            </View>
                                        )
                                    )}
                                </View>
                            </View>
                        )}

                        {requirement.brokerAssistanceRequired && (
                            <View style={styles.infoRow}>
                                <View
                                    style={[
                                        styles.assistanceChip,
                                        {
                                            backgroundColor:
                                                theme.WARNING + '20',
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="people-outline"
                                        size={16}
                                        color={theme.WARNING}
                                    />
                                    <Text
                                        style={[
                                            styles.assistanceText,
                                            { color: theme.WARNING },
                                        ]}
                                    >
                                        Broker Assistance Required
                                    </Text>
                                </View>
                            </View>
                        )}
                    </View>
                )}
            </ScrollView>

            {/* Action Buttons */}
            <View
                style={[
                    styles.actionContainer,
                    { backgroundColor: theme.CARD },
                ]}
            >
                <TouchableOpacity
                    style={[
                        styles.rejectButton,
                        { backgroundColor: theme.ERROR },
                    ]}
                    onPress={handleReject}
                    disabled={isProcessing}
                >
                    {isProcessing && rejectMutation.isLoading ? (
                        <ActivityIndicator
                            size="small"
                            color={theme.BACKGROUND}
                        />
                    ) : (
                        <>
                            <Ionicons
                                name="close-circle"
                                size={20}
                                color={theme.BACKGROUND}
                            />
                            <Text
                                style={[
                                    styles.buttonText,
                                    { color: theme.BACKGROUND },
                                ]}
                            >
                                Reject
                            </Text>
                        </>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={[
                        styles.acceptButton,
                        { backgroundColor: theme.PRIMARY },
                    ]}
                    onPress={handleAccept}
                    disabled={isProcessing}
                >
                    {isProcessing && acceptMutation.isLoading ? (
                        <ActivityIndicator
                            size="small"
                            color={theme.BACKGROUND}
                        />
                    ) : (
                        <>
                            <Ionicons
                                name="checkmark-circle"
                                size={20}
                                color={theme.BACKGROUND}
                            />
                            <Text
                                style={[
                                    styles.buttonText,
                                    { color: theme.BACKGROUND },
                                ]}
                            >
                                Accept
                            </Text>
                        </>
                    )}
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: '600',
    },
    headerRight: {
        width: 40,
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    section: {
        borderRadius: 12,
        padding: 16,
        marginTop: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
    },
    infoRow: {
        marginBottom: 12,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 4,
    },
    value: {
        fontSize: 16,
        fontWeight: '400',
        lineHeight: 22,
    },
    actionContainer: {
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingVertical: 16,
        gap: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    rejectButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 14,
        borderRadius: 8,
        gap: 8,
    },
    acceptButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 14,
        borderRadius: 8,
        gap: 8,
    },
    buttonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    loadingText: {
        fontSize: 16,
        marginTop: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
        paddingHorizontal: 40,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 8,
        textAlign: 'center',
    },
    errorSubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
    },
    preferencesGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginTop: 8,
    },
    preferenceChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
    },
    preferenceText: {
        fontSize: 12,
        fontWeight: '500',
    },
    facilitiesContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginTop: 4,
    },
    facilityChip: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 12,
    },
    facilityText: {
        fontSize: 12,
        fontWeight: '500',
    },
    assistanceChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        gap: 6,
        alignSelf: 'flex-start',
    },
    assistanceText: {
        fontSize: 14,
        fontWeight: '500',
    },
});
