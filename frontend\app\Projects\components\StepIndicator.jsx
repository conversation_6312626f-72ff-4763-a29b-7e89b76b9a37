import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const StepIndicator = ({ currentStep, totalSteps, theme }) => {
    return (
        <View style={styles.container}>
            <View style={styles.stepsContainer}>
                {Array.from({ length: totalSteps }, (_, index) => {
                    const stepNumber = index + 1;
                    const isActive = stepNumber === currentStep;
                    const isCompleted = stepNumber < currentStep;

                    return (
                        <View key={stepNumber} style={styles.stepWrapper}>
                            <View
                                style={[
                                    styles.stepCircle,
                                    {
                                        backgroundColor:
                                            isCompleted || isActive
                                                ? theme.PRIMARY
                                                : theme.INPUT_BACKGROUND,
                                        borderColor:
                                            isCompleted || isActive
                                                ? theme.PRIMARY
                                                : theme.INPUT_BORDER,
                                    },
                                ]}
                            >
                                {isCompleted ? (
                                    <Ionicons
                                        name="checkmark"
                                        size={16}
                                        color={theme.WHITE}
                                    />
                                ) : (
                                    <Text
                                        style={[
                                            styles.stepNumber,
                                            {
                                                color: isActive
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY,
                                            },
                                        ]}
                                    >
                                        {stepNumber}
                                    </Text>
                                )}
                            </View>
                            {stepNumber < totalSteps && (
                                <View
                                    style={[
                                        styles.stepLine,
                                        {
                                            backgroundColor: isCompleted
                                                ? theme.PRIMARY
                                                : theme.INPUT_BORDER,
                                        },
                                    ]}
                                />
                            )}
                        </View>
                    );
                })}
            </View>
            <Text style={[styles.stepText, { color: theme.TEXT_SECONDARY }]}>
                Step {currentStep} of {totalSteps}
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        marginBottom: 24,
    },
    stepsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    stepWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    stepCircle: {
        width: 32,
        height: 32,
        borderRadius: 16,
        borderWidth: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    stepNumber: {
        fontSize: 14,
        fontWeight: '600',
    },
    stepLine: {
        width: 40,
        height: 2,
        marginHorizontal: 8,
    },
    stepText: {
        fontSize: 14,
        fontWeight: '500',
    },
});

export default StepIndicator;
