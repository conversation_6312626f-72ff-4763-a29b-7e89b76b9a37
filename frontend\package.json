{"name": "build-connect", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "format": "prettier . --write", "lint": "eslint .", "prettier:check": "prettier --check .", "lint:fix": "eslint . --fix", "check-unused-deps": "depcheck --ignores=\"react-native,babel-plugin-*,@babel/core,typescript\""}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@stripe/stripe-react-native": "^0.50.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "expo": "~53.0.11", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.1", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "^18.1.6", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "formik": "^2.4.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "^2.27.1", "react-native-maps": "1.20.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.3.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "depcheck": "^1.4.7", "eslint": "^8.57.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-no-autofix": "^2.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "typescript": "~5.8.3"}, "private": true}