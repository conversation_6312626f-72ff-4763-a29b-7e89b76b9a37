import React, { useContext, useState, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Alert,
    Dimensions,
    Animated,
    Easing,
    Image,
    ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchUserProjects } from '../../api/projects/projectApi';
import { sendServiceRequest } from '../../api/serviceRequests/serviceRequestApi';
import Toast from 'react-native-toast-message';

const { width, height } = Dimensions.get('window');

export default function SelectProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { recipientId, recipientName, recipientType } =
        useLocalSearchParams();
    const queryClient = useQueryClient();

    const {
        data: projectsData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['userProjects'],
        queryFn: fetchUserProjects,
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to fetch projects',
            });
        },
    });

    const sendRequestMutation = useMutation({
        mutationFn: sendServiceRequest,
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Service request sent successfully',
            });
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to send service request',
            });
        },
    });

    const projects = projectsData?.projects || [];

    const handleSelectProject = (project) => {
        Alert.alert(
            'Send Service Request',
            `Send service request to ${recipientName} for "${project.projectName}"?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Send Request',
                    onPress: () => {
                        sendRequestMutation.mutate({
                            recipientId: recipientId,
                            recipientType: recipientType,
                            projectId: project._id,
                        });
                    },
                },
            ]
        );
    };

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Budget not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Budget not specified';
    };

    const ProjectCard = ({ project }) => {
        const canHire =
            recipientType === 'broker'
                ? !project.brokerId
                : recipientType === 'contractor'
                  ? (!project.brokerAssistanceRequired || project.brokerId) &&
                    !project.contractorId
                  : true;

        return (
            <TouchableOpacity
                style={[
                    styles.projectCard,
                    {
                        backgroundColor: theme.CARD,
                        opacity: canHire ? 1 : 0.6,
                    },
                ]}
                onPress={() => canHire && handleSelectProject(project)}
                activeOpacity={0.8}
                disabled={!canHire || sendRequestMutation.isLoading}
            >
                <View style={styles.cardHeader}>
                    <View style={styles.projectInfo}>
                        <Text
                            style={[
                                styles.projectName,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.projectName}
                        </Text>
                        <Text
                            style={[
                                styles.projectType,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {project.projectType || 'Not specified'} •{' '}
                            {project.constructionType || 'Not specified'}
                        </Text>
                    </View>
                    {!canHire && (
                        <View
                            style={[
                                styles.statusBadge,
                                { backgroundColor: theme.ERROR + '20' },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.statusText,
                                    { color: theme.ERROR },
                                ]}
                            >
                                {recipientType === 'contractor' &&
                                project.brokerAssistanceRequired &&
                                !project.brokerId
                                    ? 'Broker Required'
                                    : 'Already Hired'}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.cardContent}>
                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="location-on"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.location?.city}, {project.location?.state}
                        </Text>
                    </View>

                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="attach-money"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {formatBudget(project.budget)}
                        </Text>
                    </View>
                </View>

                {canHire && (
                    <View style={styles.cardFooter}>
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={styles.selectButton}
                        >
                            <Text
                                style={[
                                    styles.selectButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                {sendRequestMutation.isLoading
                                    ? 'Sending...'
                                    : 'Select Project'}
                            </Text>
                        </LinearGradient>
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons
                name="assignment"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Projects Available
            </Text>
            <Text
                style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                Create a project first to send service requests to professionals
            </Text>
            <TouchableOpacity
                style={styles.createButton}
                onPress={() => router.push('/Projects/CreateProject')}
                activeOpacity={0.8}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.gradient}
                />
                <Text style={[styles.createButtonText, { color: theme.WHITE }]}>
                    Create Project
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.CARD }]}>
                <BackButton
                    color={theme.TEXT_PRIMARY}
                    onPress={() => router.back()}
                />
                <Text
                    style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}
                    numberOfLines={1}
                >
                    Select Project for {recipientName}
                </Text>
                <View style={{ width: 24 }} />
            </View>

            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.content}>
                    {isLoading ? (
                        <View style={styles.loadingContainer}>
                            <Text
                                style={[
                                    styles.loadingText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Loading projects...
                            </Text>
                        </View>
                    ) : projects.length === 0 ? (
                        <EmptyState />
                    ) : (
                        <View style={styles.projectsList}>
                            <Text
                                style={[
                                    styles.instructionText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Select a project to send service request to{' '}
                                {recipientName}
                            </Text>
                            {projects.map((project) => (
                                <ProjectCard
                                    key={project._id}
                                    project={project}
                                />
                            ))}
                        </View>
                    )}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 15,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '600',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 10,
    },
    scrollView: {
        flex: 1,
        marginBottom: 80,
    },
    content: {
        padding: 20,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 50,
    },
    loadingText: {
        fontSize: 16,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 50,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 10,
    },
    emptySubtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 30,
        paddingHorizontal: 20,
    },
    createButton: {
        borderRadius: 25,
        overflow: 'hidden',
    },
    gradient: {
        paddingHorizontal: 30,
        paddingVertical: 12,
    },
    createButtonText: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    projectsList: {
        gap: 15,
    },
    instructionText: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 20,
        paddingHorizontal: 10,
    },
    projectCard: {
        borderRadius: 12,
        padding: 15,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 10,
    },
    projectInfo: {
        flex: 1,
        marginRight: 10,
    },
    projectName: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
    },
    projectType: {
        fontSize: 14,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 6,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    cardContent: {
        gap: 8,
        marginBottom: 15,
    },
    detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    detailText: {
        fontSize: 14,
        flex: 1,
    },
    cardFooter: {
        alignItems: 'center',
    },
    selectButton: {
        borderRadius: 8,
        paddingHorizontal: 20,
        paddingVertical: 10,
        minWidth: 120,
    },
    selectButtonText: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
    },
});
